// التطبيق الرئيسي
class PrayerTimesApp {
    constructor() {
        this.currentCity = 'الرياض';
        this.currentCountry = 'السعودية';
        this.timeFormat24 = false;
        this.adhanEnabled = true;
        this.volume = 50;
        this.currentBackground = 1;
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.updateDisplay();
        this.startClocks();
        
        // تحديث كل ثانية
        setInterval(() => {
            this.updateDisplay();
        }, 1000);
    }

    loadSettings() {
        // تحميل الإعدادات من localStorage
        const settings = localStorage.getItem('prayerAppSettings');
        if (settings) {
            const parsed = JSON.parse(settings);
            this.currentCity = parsed.city || 'الرياض';
            this.currentCountry = parsed.country || 'السعودية';
            this.timeFormat24 = parsed.timeFormat24 || false;
            this.adhanEnabled = parsed.adhanEnabled !== false;
            this.volume = parsed.volume || 50;
            this.currentBackground = parsed.background || 1;
        }
        
        this.applySettings();
    }

    saveSettings() {
        const settings = {
            city: this.currentCity,
            country: this.currentCountry,
            timeFormat24: this.timeFormat24,
            adhanEnabled: this.adhanEnabled,
            volume: this.volume,
            background: this.currentBackground
        };
        localStorage.setItem('prayerAppSettings', JSON.stringify(settings));
    }

    applySettings() {
        // تطبيق الإعدادات على الواجهة
        document.getElementById('timeFormat').checked = this.timeFormat24;
        document.getElementById('adhanEnabled').checked = this.adhanEnabled;
        document.getElementById('volumeSlider').value = this.volume;
        
        // تطبيق الخلفية
        this.changeBackground(this.currentBackground);
        
        // تحديث عرض الموقع
        document.getElementById('currentLocation').querySelector('.location-name').textContent = 
            `${this.currentCity}، ${this.currentCountry}`;
    }

    setupEventListeners() {
        // قائمة الإعدادات
        const settingsBtn = document.getElementById('settingsBtn');
        const dropdownMenu = document.getElementById('dropdownMenu');

        if (settingsBtn && dropdownMenu) {
            settingsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                dropdownMenu.classList.toggle('active');
                console.log('Settings button clicked, dropdown active:', dropdownMenu.classList.contains('active'));
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!dropdownMenu.contains(e.target) && !settingsBtn.contains(e.target)) {
                    dropdownMenu.classList.remove('active');
                }
            });
        } else {
            console.error('Settings elements not found:', { settingsBtn, dropdownMenu });
        }

        // إعدادات الوقت
        document.getElementById('timeFormat').addEventListener('change', (e) => {
            this.timeFormat24 = e.target.checked;
            this.saveSettings();
        });

        document.getElementById('adhanEnabled').addEventListener('change', (e) => {
            this.adhanEnabled = e.target.checked;
            this.saveSettings();
        });

        document.getElementById('volumeSlider').addEventListener('input', (e) => {
            this.volume = e.target.value;
            this.saveSettings();
        });

        // خيارات الخلفية
        document.querySelectorAll('.bg-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const bgNumber = parseInt(e.target.dataset.bg);
                this.changeBackground(bgNumber);
                this.currentBackground = bgNumber;
                this.saveSettings();
            });
        });

        // البحث في المدن
        const citySearch = document.getElementById('citySearch');
        citySearch.addEventListener('input', (e) => {
            this.filterCities(e.target.value);
        });
    }

    updateDisplay() {
        this.updateDigitalClock();
        this.updateAnalogClock();
        this.updatePrayerCircle();
        this.updatePrayerTimes();
    }

    updateDigitalClock() {
        const now = new Date();
        const timeDisplay = document.getElementById('timeDisplay');
        const hijriDate = document.getElementById('hijriDate');
        const gregorianDate = document.getElementById('gregorianDate');

        // عرض الوقت
        let hours = now.getHours();
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');

        if (!this.timeFormat24) {
            const ampm = hours >= 12 ? 'م' : 'ص';
            hours = hours % 12;
            hours = hours ? hours : 12;
            timeDisplay.textContent = `${hours}:${minutes}:${seconds} ${ampm}`;
        } else {
            timeDisplay.textContent = `${hours.toString().padStart(2, '0')}:${minutes}:${seconds}`;
        }

        // عرض التاريخ
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        gregorianDate.textContent = now.toLocaleDateString('ar-SA', options);
        
        // التاريخ الهجري (تقريبي)
        const hijriOptions = { 
            calendar: 'islamic-umalqura',
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        hijriDate.textContent = now.toLocaleDateString('ar-SA', hijriOptions);
    }

    updateAnalogClock() {
        const now = new Date();
        const hours = now.getHours() % 12;
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        const hourAngle = (hours * 30) + (minutes * 0.5);
        const minuteAngle = minutes * 6;
        const secondAngle = seconds * 6;

        document.getElementById('hourHand').style.transform = `rotate(${hourAngle}deg)`;
        document.getElementById('minuteHand').style.transform = `rotate(${minuteAngle}deg)`;
        document.getElementById('secondHand').style.transform = `rotate(${secondAngle}deg)`;
    }

    updatePrayerCircle() {
        // هذه دالة مؤقتة - سيتم تطويرها في المرحلة القادمة
        const circle = document.getElementById('progressCircle');
        const now = new Date();
        const totalSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();
        const daySeconds = 24 * 3600;
        const progress = (totalSeconds / daySeconds) * 565.48;
        
        circle.style.strokeDashoffset = 565.48 - progress;
    }

    updatePrayerTimes() {
        // مواقيت افتراضية - سيتم تطويرها في المرحلة القادمة
        const prayerTimes = {
            fajr: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '18:20',
            isha: '19:45'
        };

        // تحديث عرض مواقيت الصلاة
        Object.keys(prayerTimes).forEach(prayer => {
            const card = document.querySelector(`[data-prayer="${prayer}"]`);
            if (card) {
                const timeElement = card.querySelector('.prayer-time');
                timeElement.textContent = prayerTimes[prayer];
            }
        });

        // تحديد الصلاة الحالية
        this.highlightCurrentPrayer(prayerTimes);
    }

    highlightCurrentPrayer(prayerTimes) {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        // إزالة التمييز من جميع البطاقات
        document.querySelectorAll('.prayer-time-card').forEach(card => {
            card.classList.remove('active');
        });

        // تحويل مواقيت الصلاة إلى دقائق
        const times = {};
        Object.keys(prayerTimes).forEach(prayer => {
            const [hours, minutes] = prayerTimes[prayer].split(':').map(Number);
            times[prayer] = hours * 60 + minutes;
        });

        // تحديد الصلاة الحالية أو القادمة
        let nextPrayer = 'fajr';
        let nextTime = times.fajr + 24 * 60; // الفجر في اليوم التالي

        Object.keys(times).forEach(prayer => {
            if (times[prayer] > currentTime && times[prayer] < nextTime) {
                nextPrayer = prayer;
                nextTime = times[prayer];
            }
        });

        // تمييز الصلاة القادمة
        const nextCard = document.querySelector(`[data-prayer="${nextPrayer}"]`);
        if (nextCard) {
            nextCard.classList.add('active');
        }

        // تحديث دائرة العد التنازلي
        const timeRemaining = nextTime - currentTime;
        const hours = Math.floor(timeRemaining / 60);
        const minutes = timeRemaining % 60;
        
        document.getElementById('nextPrayer').textContent = this.getPrayerNameArabic(nextPrayer);
        document.getElementById('timeRemaining').textContent = 
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
        document.getElementById('nextPrayerTime').textContent = prayerTimes[nextPrayer];
    }

    getPrayerNameArabic(prayer) {
        const names = {
            fajr: 'الفجر',
            dhuhr: 'الظهر',
            asr: 'العصر',
            maghrib: 'المغرب',
            isha: 'العشاء'
        };
        return names[prayer] || prayer;
    }

    changeBackground(bgNumber) {
        const backgrounds = {
            1: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            2: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            3: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        };

        const container = document.querySelector('.background-container');
        container.style.background = backgrounds[bgNumber] || backgrounds[1];

        // تحديث الأزرار
        document.querySelectorAll('.bg-option').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-bg="${bgNumber}"]`).classList.add('active');
    }

    filterCities(searchTerm) {
        // سيتم تطوير هذه الدالة في المرحلة القادمة
        console.log('البحث عن:', searchTerm);
    }

    startClocks() {
        // بدء تشغيل الساعات
        this.updateDisplay();
    }
}

// بدء التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.prayerApp = new PrayerTimesApp();

    // تهيئة مدير الإعدادات
    if (typeof SettingsManager !== 'undefined') {
        window.settingsManager = new SettingsManager();
    }
});

