// نظام الساعات المحسن
class EnhancedClockSystem {
    constructor() {
        this.clockManager = null;
        this.prayerTimesManager = null;
        this.settingsManager = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    initialize() {
        // تهيئة جميع المدراء
        this.clockManager = new ClockManager();
        this.prayerTimesManager = new PrayerTimesManager();
        this.settingsManager = new SettingsManager();
        
        // ربط الأحداث
        this.setupEventListeners();
        
        // بدء التحديثات
        this.startUpdates();
        
        this.isInitialized = true;
        console.log('تم تهيئة نظام الساعات المحسن');
    }

    setupEventListeners() {
        // أحداث تغيير الإعدادات
        document.addEventListener('settingsChanged', (e) => {
            this.handleSettingsChange(e.detail);
        });

        // أحداث تغيير المدينة
        document.addEventListener('cityChanged', (e) => {
            this.handleCityChange(e.detail);
        });

        // أحداث وقت الصلاة
        document.addEventListener('prayerTime', (e) => {
            this.handlePrayerTime(e.detail);
        });

        // أحداث لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // أحداث اللمس للأجهزة المحمولة
        this.setupTouchEvents();
    }

    handleSettingsChange(settings) {
        if (settings.timeFormat !== undefined) {
            this.clockManager.setTimeFormat(settings.timeFormat);
        }
        
        if (settings.timezone) {
            this.clockManager.setTimezone(settings.timezone);
        }
        
        if (settings.adhanEnabled !== undefined) {
            this.prayerTimesManager.setAdhanEnabled(settings.adhanEnabled);
        }
        
        if (settings.volume !== undefined) {
            this.prayerTimesManager.setVolume(settings.volume);
        }
    }

    handleCityChange(cityData) {
        this.prayerTimesManager.updateLocation(
            cityData.name,
            cityData.country,
            cityData.latitude,
            cityData.longitude
        );
        
        // تحديث المنطقة الزمنية
        if (cityData.timezone) {
            this.clockManager.setTimezone(cityData.timezone);
        }
    }

    handlePrayerTime(prayerData) {
        // تشغيل الأذان
        if (this.prayerTimesManager.isAdhanEnabled()) {
            this.playAdhan(prayerData.prayer);
        }
        
        // إظهار إشعار
        this.showPrayerNotification(prayerData);
        
        // تحديث الواجهة
        this.updatePrayerHighlight(prayerData.prayer);
    }

    handleKeyboardShortcuts(e) {
        // اختصارات لوحة المفاتيح
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    this.toggleSettings();
                    break;
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'm':
                    e.preventDefault();
                    this.toggleMute();
                    break;
            }
        }
        
        // مفاتيح الأسهم للتنقل
        if (e.key === 'Escape') {
            this.closeAllMenus();
        }
    }

    setupTouchEvents() {
        // دعم اللمس للأجهزة المحمولة
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // التمرير الأفقي لتغيير الخلفية
            if (Math.abs(deltaX) > 100 && Math.abs(deltaY) < 50) {
                if (deltaX > 0) {
                    this.nextBackground();
                } else {
                    this.previousBackground();
                }
            }
            
            // التمرير العمودي لفتح/إغلاق الإعدادات
            if (Math.abs(deltaY) > 100 && Math.abs(deltaX) < 50) {
                if (deltaY < 0) {
                    this.openSettings();
                } else {
                    this.closeSettings();
                }
            }
        });
    }

    startUpdates() {
        // تحديث الساعات كل ثانية
        setInterval(() => {
            if (this.isInitialized) {
                this.updateAllClocks();
                this.updatePrayerCountdown();
                this.updateWeatherInfo();
            }
        }, 1000);
        
        // تحديث مواقيت الصلاة كل دقيقة
        setInterval(() => {
            if (this.isInitialized) {
                this.checkPrayerTimes();
            }
        }, 60000);
        
        // تحديث الطقس كل 30 دقيقة
        setInterval(() => {
            if (this.isInitialized) {
                this.fetchWeatherData();
            }
        }, 1800000);
    }

    updateAllClocks() {
        const now = new Date();
        
        // تحديث الساعة التناظرية
        this.updateAnalogClock(now);
        
        // تحديث الساعة الرقمية
        this.updateDigitalClock(now);
        
        // تحديث التاريخ
        this.updateDateDisplay(now);
    }

    updateAnalogClock(now) {
        const hours = now.getHours() % 12;
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        // حساب الزوايا مع انيميشن سلس
        const hourAngle = (hours * 30) + (minutes * 0.5) + (seconds * 0.00833);
        const minuteAngle = (minutes * 6) + (seconds * 0.1);
        const secondAngle = seconds * 6;

        // تطبيق الدوران
        const hourHand = document.getElementById('hourHand');
        const minuteHand = document.getElementById('minuteHand');
        const secondHand = document.getElementById('secondHand');

        if (hourHand) {
            hourHand.style.transform = `rotate(${hourAngle}deg)`;
        }

        if (minuteHand) {
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
        }

        if (secondHand) {
            secondHand.style.transform = `rotate(${secondAngle}deg)`;
            // إضافة تأثير نبضة كل ثانية
            if (seconds === 0) {
                secondHand.style.animation = 'pulse 0.3s ease';
                setTimeout(() => {
                    secondHand.style.animation = '';
                }, 300);
            }
        }
    }

    updateDigitalClock(now) {
        const timeDisplay = document.getElementById('timeDisplay');
        if (!timeDisplay) return;

        let hours = now.getHours();
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');

        let timeString;
        if (this.clockManager && this.clockManager.is24Hour) {
            timeString = `${hours.toString().padStart(2, '0')}:${minutes}:${seconds}`;
        } else {
            const ampm = hours >= 12 ? 'م' : 'ص';
            hours = hours % 12;
            hours = hours ? hours : 12;
            timeString = `${hours}:${minutes}:${seconds} ${ampm}`;
        }

        // تأثير تغيير الوقت
        if (timeDisplay.textContent !== timeString) {
            timeDisplay.style.transform = 'scale(1.05)';
            timeDisplay.textContent = timeString;
            setTimeout(() => {
                timeDisplay.style.transform = 'scale(1)';
            }, 200);
        }
    }

    updateDateDisplay(now) {
        const hijriDate = document.getElementById('hijriDate');
        const gregorianDate = document.getElementById('gregorianDate');

        if (gregorianDate) {
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            gregorianDate.textContent = now.toLocaleDateString('ar-SA', options);
        }

        if (hijriDate) {
            try {
                const hijriOptions = {
                    calendar: 'islamic-umalqura',
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    numberingSystem: 'arab'
                };
                const hijriText = now.toLocaleDateString('ar-SA', hijriOptions);
                hijriDate.textContent = hijriText;
            } catch (error) {
                console.log('Fallback to manual Hijri calculation');
                hijriDate.textContent = this.calculateHijriDate(now);
            }
        }
    }

    updatePrayerCountdown() {
        if (this.prayerTimesManager) {
            this.prayerTimesManager.updateCountdown();
        }
    }

    checkPrayerTimes() {
        if (this.prayerTimesManager) {
            this.prayerTimesManager.checkCurrentPrayerTime();
        }
    }

    async fetchWeatherData() {
        // جلب بيانات الطقس (مبسط)
        try {
            const weatherInfo = document.getElementById('weatherInfo');
            if (weatherInfo) {
                // محاكاة بيانات الطقس
                const temperatures = [20, 22, 25, 28, 30, 32, 35];
                const temp = temperatures[Math.floor(Math.random() * temperatures.length)];
                const tempElement = weatherInfo.querySelector('.temperature');
                if (tempElement) {
                    tempElement.textContent = `${temp}°`;
                }
            }
        } catch (error) {
            console.log('خطأ في جلب بيانات الطقس:', error);
        }
    }

    // وظائف التحكم
    toggleSettings() {
        const dropdown = document.getElementById('dropdownMenu');
        if (dropdown) {
            dropdown.classList.toggle('active');
        }
    }

    openSettings() {
        const dropdown = document.getElementById('dropdownMenu');
        if (dropdown) {
            dropdown.classList.add('active');
        }
    }

    closeSettings() {
        const dropdown = document.getElementById('dropdownMenu');
        if (dropdown) {
            dropdown.classList.remove('active');
        }
    }

    closeAllMenus() {
        this.closeSettings();
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('لا يمكن تفعيل وضع ملء الشاشة:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    toggleMute() {
        const adhanPlayer = document.getElementById('adhanPlayer');
        if (adhanPlayer) {
            adhanPlayer.muted = !adhanPlayer.muted;
            this.showNotification(adhanPlayer.muted ? 'تم كتم الصوت' : 'تم إلغاء كتم الصوت');
        }
    }

    nextBackground() {
        if (window.prayerApp) {
            const currentBg = window.prayerApp.currentBackground;
            const nextBg = currentBg >= 3 ? 1 : currentBg + 1;
            window.prayerApp.changeBackground(nextBg);
            window.prayerApp.currentBackground = nextBg;
            window.prayerApp.saveSettings();
        }
    }

    previousBackground() {
        if (window.prayerApp) {
            const currentBg = window.prayerApp.currentBackground;
            const prevBg = currentBg <= 1 ? 3 : currentBg - 1;
            window.prayerApp.changeBackground(prevBg);
            window.prayerApp.currentBackground = prevBg;
            window.prayerApp.saveSettings();
        }
    }

    playAdhan(prayerName) {
        const adhanPlayer = document.getElementById('adhanPlayer');
        if (adhanPlayer && window.prayerApp && window.prayerApp.adhanEnabled) {
            adhanPlayer.volume = window.prayerApp.volume / 100;
            adhanPlayer.play().catch(error => {
                console.log('لا يمكن تشغيل الأذان:', error);
            });
        }
    }

    showPrayerNotification(prayerData) {
        const notification = document.createElement('div');
        notification.className = 'prayer-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">🕌</div>
                <h3>حان وقت ${prayerData.arabicName}</h3>
                <p>الوقت: ${prayerData.time}</p>
                <div class="notification-actions">
                    <button onclick="this.closest('.prayer-notification').remove()">إغلاق</button>
                    <button onclick="this.closest('.prayer-notification').remove(); window.enhancedClock.stopAdhan()">إيقاف الأذان</button>
                </div>
            </div>
        `;

        // تطبيق الأنماط
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            z-index: 10000;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.5s ease;
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => notification.remove(), 500);
            }
        }, 10000);
    }

    stopAdhan() {
        const adhanPlayer = document.getElementById('adhanPlayer');
        if (adhanPlayer) {
            adhanPlayer.pause();
            adhanPlayer.currentTime = 0;
        }
    }

    updatePrayerHighlight(currentPrayer) {
        // إزالة التمييز من جميع البطاقات
        document.querySelectorAll('.prayer-time-card').forEach(card => {
            card.classList.remove('active');
        });

        // تمييز الصلاة الحالية
        const currentCard = document.querySelector(`[data-prayer="${currentPrayer}"]`);
        if (currentCard) {
            currentCard.classList.add('active');
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 217, 100, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 10000;
            font-weight: 600;
            backdrop-filter: blur(10px);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    calculateHijriDate(gregorianDate) {
        // حساب تقريبي للتاريخ الهجري
        const hijriEpoch = new Date('622-07-16');
        const daysDiff = Math.floor((gregorianDate - hijriEpoch) / (1000 * 60 * 60 * 24));
        const hijriYear = Math.floor(daysDiff / 354.37) + 1;
        const remainingDays = daysDiff % 354.37;
        const hijriMonth = Math.floor(remainingDays / 29.53) + 1;
        const hijriDay = Math.floor(remainingDays % 29.53) + 1;

        const hijriMonths = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
            'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ];

        return `${hijriDay} ${hijriMonths[hijriMonth - 1]} ${hijriYear} هـ`;
    }
}

// إضافة أنماط CSS للانيميشن
const enhancedStyles = `
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notification-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.notification-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.notification-actions {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.notification-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.notification-actions button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.time-display {
    transition: transform 0.2s ease;
}

.hand {
    transition: transform 0.1s ease;
}
`;

// إضافة الأنماط إلى الصفحة
const enhancedStyleSheet = document.createElement('style');
enhancedStyleSheet.textContent = enhancedStyles;
document.head.appendChild(enhancedStyleSheet);

// تهيئة النظام المحسن
window.enhancedClock = new EnhancedClockSystem();

