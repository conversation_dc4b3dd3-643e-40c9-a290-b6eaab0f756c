/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
    position: relative;
}

/* خلفية التطبيق */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* قائمة الإعدادات */
.settings-menu {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 16px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 20px;
    min-width: 300px;
    max-height: 500px;
    overflow-y: auto;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.dropdown-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.dropdown-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dropdown-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.dropdown-section h3 {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

/* المستطيل العمودي */
.vertical-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border-left: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 30px 20px;
    z-index: 100;
}

/* الساعة التناظرية */
.analog-clock-container {
    width: 150px;
    height: 150px;
}

.analog-clock {
    width: 100%;
    height: 100%;
    position: relative;
}

.clock-face {
    width: 100%;
    height: 100%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    position: relative;
    backdrop-filter: blur(10px);
}

.hour-markers {
    position: absolute;
    width: 100%;
    height: 100%;
}

.hour-markers::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    width: 3px;
    height: 20px;
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(-50%);
    box-shadow: 
        0 170px 0 rgba(255, 255, 255, 0.8),
        85px 85px 0 rgba(255, 255, 255, 0.6),
        -85px 85px 0 rgba(255, 255, 255, 0.6);
}

.clock-hands {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hand {
    position: absolute;
    background: white;
    transform-origin: bottom center;
    border-radius: 2px;
}

.hour-hand {
    width: 3px;
    height: 35px;
    top: -35px;
    left: -1.5px;
    background: #fff;
}

.minute-hand {
    width: 2px;
    height: 50px;
    top: -50px;
    left: -1px;
    background: #fff;
}

.second-hand {
    width: 1px;
    height: 55px;
    top: -55px;
    left: -0.5px;
    background: #ff4757;
}

.center-dot {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: -6px;
    left: -6px;
    z-index: 10;
}

/* الساعة الرقمية */
.digital-clock-container {
    text-align: center;
    color: white;
}

.time-display {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    font-family: 'Cairo', monospace;
}

.date-display {
    font-size: 14px;
    opacity: 0.9;
}

.hijri-date, .gregorian-date {
    margin: 2px 0;
}

/* الساعة الدائرية لمواقيت الصلاة */
.prayer-circle-container {
    width: 180px;
    height: 180px;
    position: relative;
}

.prayer-circle {
    width: 100%;
    height: 100%;
    position: relative;
}

.circle-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 8;
}

.circle-progress {
    fill: none;
    stroke: #4ecdc4;
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 565.48;
    stroke-dashoffset: 565.48;
    transition: stroke-dashoffset 1s ease;
}

.circle-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.next-prayer {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.time-remaining {
    font-size: 24px;
    font-weight: 700;
    color: #4ecdc4;
    margin-bottom: 5px;
}

.prayer-time {
    font-size: 14px;
    opacity: 0.8;
}

/* المستطيل الأفقي */
.horizontal-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 300px;
    height: 150px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border-top: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.prayer-times-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100px;
    padding: 0 30px;
}

.prayer-time-card {
    text-align: center;
    color: white;
    padding: 15px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    min-width: 120px;
    position: relative;
}

.prayer-time-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.prayer-time-card.active {
    background: rgba(76, 217, 100, 0.3);
    border: 2px solid #4cd964;
}

.prayer-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.prayer-time {
    font-size: 20px;
    font-weight: 700;
    color: #4ecdc4;
}

.prayer-status {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4cd964;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.prayer-time-card.active .prayer-status {
    opacity: 1;
}

/* النص المتحرك */
.scrolling-text {
    height: 50px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
}

.text-content {
    color: white;
    font-size: 18px;
    font-weight: 500;
    white-space: nowrap;
    animation: scroll 30s linear infinite;
    padding-right: 100%;
}

@keyframes scroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* المحتوى الرئيسي */
.main-content {
    position: fixed;
    top: 30px;
    right: 320px;
    left: 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    z-index: 50;
}

.current-location {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    border-radius: 25px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.weather-info {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    border-radius: 25px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* مفاتيح التبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 10px 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4ecdc4;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* البحث */
.search-container {
    margin-bottom: 15px;
}

.search-container input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Cairo', sans-serif;
}

.cities-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

.city-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    transition: background 0.2s ease;
}

.city-item:hover {
    background: #f5f5f5;
}

.city-item:last-child {
    border-bottom: none;
}

/* التحكم في الصوت */
.volume-control {
    margin: 10px 0;
}

.volume-control label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #333;
}

.volume-control input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

/* خيارات الخلفية */
.background-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.bg-option {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.bg-option:hover {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
}

.bg-option.active {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .vertical-panel {
        width: 100%;
        height: auto;
        position: relative;
        flex-direction: row;
        justify-content: space-around;
        padding: 20px;
    }
    
    .horizontal-panel {
        right: 0;
        position: relative;
    }
    
    .main-content {
        position: relative;
        right: auto;
        left: auto;
        top: auto;
        margin: 20px;
    }
    
    .analog-clock-container,
    .prayer-circle-container {
        width: 120px;
        height: 120px;
    }
    
    .time-display {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .prayer-times-container {
        flex-wrap: wrap;
        height: auto;
        padding: 15px;
    }
    
    .prayer-time-card {
        min-width: 80px;
        margin: 5px;
        padding: 10px;
    }
    
    .prayer-name {
        font-size: 14px;
    }
    
    .prayer-time {
        font-size: 16px;
    }
    
    .text-content {
        font-size: 14px;
    }
}

